server:
  port: 8091

# 线程池配置
thread:
  pool:
    executor:
      config:
        core-pool-size: 20
        max-pool-size: 50
        keep-alive-time: 5000
        block-queue-size: 5000
        policy: CallerRunsPolicy

# 数据库配置；启动时配置数据库资源信息
spring:
  datasource:
    # MariaDB 统一数据源配置（支持业务数据和向量存储）
    username: root
    password: 123456
    url: *************************************************************************************************************************************************************************
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      pool-name: MariaDB_HikariCP
      minimum-idle: 15
      idle-timeout: 180000
      maximum-pool-size: 25
      auto-commit: true
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  # Spring AI 配置
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:your-openai-api-key}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      embedding:
        model: text-embedding-3-small
        dimensions: 1536
    vectorstore:
      mariadb:
        initialize-schema: true
        distance-type: COSINE
        dimensions: 1536
        table-name: vector_store
        schema-validation: true

# MyBatis 配置
mybatis:
  mapper-locations: classpath:/mybatis/mapper/*.xml
  config-location:  classpath:/mybatis/config/mybatis-config.xml

# 日志
logging:
  level:
    root: info
  config: classpath:logback-spring.xml