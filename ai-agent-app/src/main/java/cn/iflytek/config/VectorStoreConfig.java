package cn.iflytek.config;

import org.springframework.ai.embedding.EmbeddingModel;

import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.mariadb.MariaDBVectorStore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * 向量存储配置
 */
@Configuration
public class VectorStoreConfig {

    /**
     * MariaDB 向量存储
     */
    @Bean
    public VectorStore vectorStore(JdbcTemplate jdbcTemplate, EmbeddingModel embeddingModel) {
        return MariaDBVectorStore.builder(jdbcTemplate, embeddingModel)
                .dimensions(1536)
                .distanceType(MariaDBVectorStore.MariaDBDistanceType.COSINE)
                .vectorTableName("vector_store")
                .contentFieldName("content")
                .embeddingFieldName("embedding")
                .idFieldName("id")
                .metadataFieldName("metadata")
                .initializeSchema(true)
                .schemaValidation(true)
                .removeExistingVectorStoreTable(false)
                .maxDocumentBatchSize(10000)
                .build();
    }
}
