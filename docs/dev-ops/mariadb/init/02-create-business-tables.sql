-- AI Agent 业务表结构 (MariaDB)
-- 创建时间: 2025-08-03
-- 数据库: ai_agent_db

USE ai_agent_db;

-- 1. AI客户端系统提示词表
CREATE TABLE `ai_client_system_prompt` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `prompt_content` TEXT NOT NULL COMMENT '提示词内容',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI客户端系统提示词表';

-- 2. AI客户端模型配置表
CREATE TABLE `ai_client_model` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `model_name` VARCHAR(100) NOT NULL COMMENT '模型名称',
    `base_url` VARCHAR(500) NOT NULL COMMENT '基础URL',
    `api_key` VARCHAR(500) NOT NULL COMMENT 'API密钥',
    `completions_path` VARCHAR(200) DEFAULT '/v1/chat/completions' COMMENT '完成路径',
    `embeddings_path` VARCHAR(200) DEFAULT '/v1/embeddings' COMMENT '嵌入路径',
    `model_type` VARCHAR(50) NOT NULL COMMENT '模型类型(openai/azure等)',
    `model_version` VARCHAR(50) COMMENT '模型版本',
    `timeout` INT DEFAULT 60 COMMENT '超时时间(秒)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_model_name` (`model_name`),
    KEY `idx_model_type` (`model_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI客户端模型配置表';

-- 3. AI客户端模型工具配置表
CREATE TABLE `ai_client_model_tool_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `model_id` BIGINT NOT NULL COMMENT '模型ID',
    `tool_type` VARCHAR(50) NOT NULL COMMENT '工具类型(mcp/function_call)',
    `tool_id` BIGINT NOT NULL COMMENT 'MCP ID或function call ID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_model_id` (`model_id`),
    KEY `idx_tool_type_id` (`tool_type`, `tool_id`),
    CONSTRAINT `fk_model_tool_config_model` FOREIGN KEY (`model_id`) REFERENCES `ai_client_model` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI客户端模型工具配置表';

-- 4. AI客户端顾问配置表
CREATE TABLE `ai_client_advisor` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `advisor_name` VARCHAR(100) NOT NULL COMMENT '顾问名称',
    `advisor_type` VARCHAR(50) NOT NULL COMMENT '顾问类型(PromptChatMemory/RagAnswer/SimpleLoggerAdvisor等)',
    `order_num` INT NOT NULL DEFAULT 0 COMMENT '顺序号',
    `chat_memory_max_messages` INT COMMENT '记忆最大消息数',
    `rag_answer_top_k` INT DEFAULT 4 COMMENT 'RAG答案TopK',
    `rag_answer_filter_expression` VARCHAR(500) COMMENT 'RAG答案过滤表达式',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_advisor_name` (`advisor_name`),
    KEY `idx_advisor_type` (`advisor_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI客户端顾问配置表';

-- 5. AI客户端MCP工具配置表
CREATE TABLE `ai_client_tool_mcp` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `mcp_name` VARCHAR(100) NOT NULL COMMENT 'MCP名称',
    `transport_type` VARCHAR(20) NOT NULL COMMENT '传输类型(sse/stdio)',
    `sse_base_uri` VARCHAR(500) COMMENT 'SSE基础URI',
    `sse_endpoint` VARCHAR(200) COMMENT 'SSE端点',
    `stdio_config` JSON COMMENT 'STDIO配置JSON',
    `request_timeout` INT DEFAULT 5 COMMENT '请求超时时间(分钟)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_mcp_name` (`mcp_name`),
    KEY `idx_transport_type` (`transport_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI客户端MCP工具配置表';

-- 6. AI客户端主表
CREATE TABLE `ai_client` (
    `client_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '客户端ID',
    `system_prompt_id` BIGINT COMMENT '系统提示词ID',
    `model_bean_id` BIGINT NOT NULL COMMENT '模型Bean ID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`client_id`),
    KEY `idx_system_prompt_id` (`system_prompt_id`),
    KEY `idx_model_bean_id` (`model_bean_id`),
    CONSTRAINT `fk_ai_client_system_prompt` FOREIGN KEY (`system_prompt_id`) REFERENCES `ai_client_system_prompt` (`id`),
    CONSTRAINT `fk_ai_client_model` FOREIGN KEY (`model_bean_id`) REFERENCES `ai_client_model` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI客户端主表';

-- 7. AI客户端与顾问关联表
CREATE TABLE `ai_client_advisor_rel` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `client_id` BIGINT NOT NULL COMMENT '客户端ID',
    `advisor_bean_id` BIGINT NOT NULL COMMENT '顾问Bean ID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_client_advisor` (`client_id`, `advisor_bean_id`),
    KEY `idx_client_id` (`client_id`),
    KEY `idx_advisor_bean_id` (`advisor_bean_id`),
    CONSTRAINT `fk_client_advisor_rel_client` FOREIGN KEY (`client_id`) REFERENCES `ai_client` (`client_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_client_advisor_rel_advisor` FOREIGN KEY (`advisor_bean_id`) REFERENCES `ai_client_advisor` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI客户端与顾问关联表';

-- 8. AI客户端与MCP工具关联表
CREATE TABLE `ai_client_mcp_rel` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `client_id` BIGINT NOT NULL COMMENT '客户端ID',
    `mcp_bean_id` BIGINT NOT NULL COMMENT 'MCP Bean ID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_client_mcp` (`client_id`, `mcp_bean_id`),
    KEY `idx_client_id` (`client_id`),
    KEY `idx_mcp_bean_id` (`mcp_bean_id`),
    CONSTRAINT `fk_client_mcp_rel_client` FOREIGN KEY (`client_id`) REFERENCES `ai_client` (`client_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_client_mcp_rel_mcp` FOREIGN KEY (`mcp_bean_id`) REFERENCES `ai_client_tool_mcp` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI客户端与MCP工具关联表';

-- 9. RAG知识库表
CREATE TABLE `ai_rag_knowledge_base` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` VARCHAR(100) NOT NULL COMMENT '知识库名称',
    `description` TEXT COMMENT '知识库描述',
    `embedding_model` VARCHAR(100) NOT NULL COMMENT '嵌入模型名称',
    `vector_dimension` INT NOT NULL DEFAULT 1536 COMMENT '向量维度',
    `distance_type` VARCHAR(20) NOT NULL DEFAULT 'COSINE' COMMENT '距离类型(COSINE/EUCLIDEAN)',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`name`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RAG知识库表';

-- 10. RAG文档表
CREATE TABLE `ai_rag_document` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `knowledge_base_id` BIGINT NOT NULL COMMENT '知识库ID',
    `document_id` VARCHAR(100) NOT NULL COMMENT '文档唯一标识',
    `title` VARCHAR(500) NOT NULL COMMENT '文档标题',
    `content` LONGTEXT NOT NULL COMMENT '文档内容',
    `source` VARCHAR(500) COMMENT '文档来源',
    `metadata` JSON COMMENT '文档元数据',
    `chunk_size` INT DEFAULT 1000 COMMENT '分块大小',
    `chunk_overlap` INT DEFAULT 200 COMMENT '分块重叠',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态(0:禁用,1:启用,2:处理中)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_knowledge_base_document` (`knowledge_base_id`, `document_id`),
    KEY `idx_knowledge_base_id` (`knowledge_base_id`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_rag_document_knowledge_base` FOREIGN KEY (`knowledge_base_id`) REFERENCES `ai_rag_knowledge_base` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RAG文档表';

-- 11. RAG文档分块表
CREATE TABLE `ai_rag_document_chunk` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `document_id` BIGINT NOT NULL COMMENT '文档ID',
    `chunk_index` INT NOT NULL COMMENT '分块索引',
    `content` TEXT NOT NULL COMMENT '分块内容',
    `metadata` JSON COMMENT '分块元数据',
    `vector_stored` TINYINT NOT NULL DEFAULT 0 COMMENT '是否已存储向量(0:否,1:是)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_document_chunk` (`document_id`, `chunk_index`),
    KEY `idx_document_id` (`document_id`),
    KEY `idx_vector_stored` (`vector_stored`),
    CONSTRAINT `fk_rag_chunk_document` FOREIGN KEY (`document_id`) REFERENCES `ai_rag_document` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RAG文档分块表';
