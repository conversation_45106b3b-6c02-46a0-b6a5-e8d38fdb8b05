-- MariaDB AI Agent 数据库初始化脚本
-- 创建时间: 2025-08-03

-- 使用默认创建的数据库
USE ai_agent_db;

-- 创建用户并授权
CREATE USER IF NOT EXISTS 'ai_user'@'%' IDENTIFIED BY 'ai_pass';
GRANT ALL PRIVILEGES ON ai_agent_db.* TO 'ai_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 注意：Spring AI MariaDB Vector Store 会自动创建 vector_store 表
-- 表结构大致如下（仅供参考，实际由Spring AI自动创建）:
/*
CREATE TABLE IF NOT EXISTS vector_store (
    id VARCHAR(255) NOT NULL PRIMARY KEY,
    content LONGTEXT,
    metadata JSON,
    embedding VECTOR(1536) NOT NULL,
    INDEX idx_embedding USING HNSW (embedding)
);
*/
