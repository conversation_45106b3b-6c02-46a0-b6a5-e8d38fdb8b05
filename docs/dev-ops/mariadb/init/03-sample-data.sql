-- AI Agent 示例数据 (MariaDB)
-- 创建时间: 2025-08-03
-- 数据库: ai_agent_db

USE ai_agent_db;

-- 插入系统提示词示例数据
INSERT INTO ai_client_system_prompt (prompt_content) VALUES 
('你是一个专业的AI助手，请根据提供的上下文信息回答用户的问题。'),
('你是一个技术专家，专门回答关于Spring AI和RAG技术的问题。'),
('你是一个友好的客服助手，请耐心回答用户的问题。');

-- 插入模型配置示例数据
INSERT INTO ai_client_model (model_name, base_url, api_key, model_type, model_version) VALUES 
('gpt-3.5-turbo', 'https://api.openai.com', 'your-openai-api-key', 'openai', '3.5'),
('text-embedding-3-small', 'https://api.openai.com', 'your-openai-api-key', 'openai', '3.0'),
('gpt-4', 'https://api.openai.com', 'your-openai-api-key', 'openai', '4.0');

-- 插入顾问配置示例数据
INSERT INTO ai_client_advisor (advisor_name, advisor_type, order_num, chat_memory_max_messages, rag_answer_top_k, rag_answer_filter_expression) VALUES 
('记忆管理顾问', 'PromptChatMemory', 1, 10, NULL, NULL),
('RAG问答顾问', 'RagAnswer', 2, NULL, 5, 'type == "document"'),
('日志记录顾问', 'SimpleLoggerAdvisor', 3, NULL, NULL, NULL);

-- 插入MCP工具配置示例数据
INSERT INTO ai_client_tool_mcp (mcp_name, transport_type, sse_base_uri, sse_endpoint, request_timeout) VALUES 
('weather-tool', 'sse', 'http://localhost:8080', '/sse', 5),
('search-tool', 'sse', 'http://localhost:8081', '/search', 10);

-- 插入AI客户端示例数据
INSERT INTO ai_client (system_prompt_id, model_bean_id) VALUES 
(1, 1),
(2, 3),
(3, 1);

-- 插入客户端与顾问关联示例数据
INSERT INTO ai_client_advisor_rel (client_id, advisor_bean_id) VALUES 
(1, 1),
(1, 2),
(2, 2),
(2, 3),
(3, 1);

-- 插入RAG知识库示例数据
INSERT INTO ai_rag_knowledge_base (name, description, embedding_model, vector_dimension, distance_type, status) VALUES 
('技术文档库', 'Spring AI和RAG相关技术文档', 'text-embedding-3-small', 1536, 'COSINE', 1),
('产品手册库', '产品使用手册和FAQ', 'text-embedding-3-small', 1536, 'COSINE', 1),
('测试知识库', '用于测试的示例知识库', 'text-embedding-3-small', 1536, 'COSINE', 1);

-- 插入RAG文档示例数据
INSERT INTO ai_rag_document (knowledge_base_id, document_id, title, content, source, metadata, chunk_size, chunk_overlap, status) VALUES 
(1, 'spring-ai-intro', 'Spring AI简介', 'Spring AI是一个用于构建AI应用程序的框架，它提供了与各种AI模型的集成，包括OpenAI、Azure OpenAI等。框架支持聊天模型、嵌入模型、图像模型等多种AI功能。', 'https://docs.spring.io/spring-ai/', '{"category": "framework", "topic": "spring-ai", "language": "zh"}', 1000, 200, 1),
(1, 'rag-concept', 'RAG概念介绍', 'RAG（检索增强生成）是一种结合了信息检索和文本生成的AI技术。它首先从知识库中检索相关信息，然后基于检索到的信息生成回答，可以提高生成内容的准确性和相关性。', 'internal', '{"category": "concept", "topic": "rag", "language": "zh"}', 1000, 200, 1),
(2, 'vector-db-guide', '向量数据库使用指南', 'MariaDB 11.7+版本支持向量存储功能，可以存储和检索高维向量数据。向量数据库特别适合用于相似性搜索和语义搜索场景。', 'internal', '{"category": "database", "topic": "vector-db", "language": "zh"}', 1000, 200, 1),
(3, 'test-document', '测试文档', '这是一个用于测试的示例文档，包含了一些基本的文本内容，用于验证RAG系统的功能是否正常工作。', 'test', '{"category": "test", "topic": "sample", "language": "zh"}', 500, 100, 1);

-- 插入RAG文档分块示例数据
INSERT INTO ai_rag_document_chunk (document_id, chunk_index, content, metadata, vector_stored) VALUES 
(1, 0, 'Spring AI是一个用于构建AI应用程序的框架，它提供了与各种AI模型的集成，包括OpenAI、Azure OpenAI等。', '{"chunk_type": "intro", "word_count": 45}', 0),
(1, 1, '框架支持聊天模型、嵌入模型、图像模型等多种AI功能。', '{"chunk_type": "features", "word_count": 25}', 0),
(2, 0, 'RAG（检索增强生成）是一种结合了信息检索和文本生成的AI技术。', '{"chunk_type": "definition", "word_count": 30}', 0),
(2, 1, '它首先从知识库中检索相关信息，然后基于检索到的信息生成回答，可以提高生成内容的准确性和相关性。', '{"chunk_type": "process", "word_count": 50}', 0),
(3, 0, 'MariaDB 11.7+版本支持向量存储功能，可以存储和检索高维向量数据。', '{"chunk_type": "capability", "word_count": 35}', 0),
(3, 1, '向量数据库特别适合用于相似性搜索和语义搜索场景。', '{"chunk_type": "use_case", "word_count": 25}', 0),
(4, 0, '这是一个用于测试的示例文档，包含了一些基本的文本内容，用于验证RAG系统的功能是否正常工作。', '{"chunk_type": "test", "word_count": 45}', 0);

-- 显示插入结果
SELECT '数据插入完成' as status;
SELECT COUNT(*) as system_prompt_count FROM ai_client_system_prompt;
SELECT COUNT(*) as model_count FROM ai_client_model;
SELECT COUNT(*) as advisor_count FROM ai_client_advisor;
SELECT COUNT(*) as client_count FROM ai_client;
SELECT COUNT(*) as knowledge_base_count FROM ai_rag_knowledge_base;
SELECT COUNT(*) as document_count FROM ai_rag_document;
SELECT COUNT(*) as chunk_count FROM ai_rag_document_chunk;
