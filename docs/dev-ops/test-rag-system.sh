#!/bin/bash

# RAG系统快速测试脚本

BASE_URL="http://localhost:8091"
KNOWLEDGE_BASE="test-kb"

echo "=== RAG系统功能测试 ==="
echo ""

# 1. 测试向量存储状态
echo "1. 测试向量存储状态..."
curl -s "$BASE_URL/api/rag-test/test-vector-store" | jq '.'
echo ""

# 2. 创建示例知识库
echo "2. 创建示例知识库..."
curl -s -X POST "$BASE_URL/api/rag-test/create-sample-knowledge-base?ragName=$KNOWLEDGE_BASE" | jq '.'
echo ""

# 3. 获取知识库统计信息
echo "3. 获取知识库统计信息..."
curl -s "$BASE_URL/api/rag/knowledge-base/$KNOWLEDGE_BASE/stats" | jq '.'
echo ""

# 4. 测试向量搜索
echo "4. 测试向量搜索 - 查询'Spring AI'..."
curl -s "$BASE_URL/api/rag-test/test-search?ragName=$KNOWLEDGE_BASE&query=Spring%20AI&topK=3" | jq '.'
echo ""

# 5. 测试向量搜索 - 查询'数据库'
echo "5. 测试向量搜索 - 查询'数据库'..."
curl -s "$BASE_URL/api/rag-test/test-search?ragName=$KNOWLEDGE_BASE&query=数据库&topK=3" | jq '.'
echo ""

# 6. 批量添加文档测试
echo "6. 批量添加文档测试..."
curl -s -X POST "$BASE_URL/api/rag-test/test-batch-add?ragName=$KNOWLEDGE_BASE&documentCount=5" | jq '.'
echo ""

# 7. 获取所有文档
echo "7. 获取知识库中的所有文档..."
curl -s "$BASE_URL/api/rag/knowledge-base/$KNOWLEDGE_BASE/documents" | jq '.documentCount, .documents[0:2]'
echo ""

# 8. 测试RAG问答（如果配置了ChatClient）
echo "8. 测试RAG问答..."
curl -s -X POST "$BASE_URL/api/rag-test/test-qa?ragName=$KNOWLEDGE_BASE&question=什么是Spring%20AI？" | jq '.'
echo ""

# 9. 测试高级搜索（带过滤条件）
echo "9. 测试高级搜索（带过滤条件）..."
curl -s -X POST "$BASE_URL/api/rag/knowledge-base/$KNOWLEDGE_BASE/search?query=框架&topK=3&filterExpression=category=='framework'" | jq '.'
echo ""

# 10. 清理测试数据
echo "10. 是否清理测试数据？(y/n)"
read -r cleanup
if [ "$cleanup" = "y" ] || [ "$cleanup" = "Y" ]; then
    echo "清理测试数据..."
    curl -s -X DELETE "$BASE_URL/api/rag-test/cleanup?ragName=$KNOWLEDGE_BASE" | jq '.'
    echo "测试数据已清理"
else
    echo "保留测试数据"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如需手动测试，可以使用以下API："
echo "- 创建知识库: POST $BASE_URL/api/rag/knowledge-base"
echo "- 添加文档: POST $BASE_URL/api/rag/knowledge-base/{ragName}/documents"
echo "- 搜索文档: POST $BASE_URL/api/rag/knowledge-base/{ragName}/search"
echo "- 获取统计: GET $BASE_URL/api/rag/knowledge-base/{ragName}/stats"
