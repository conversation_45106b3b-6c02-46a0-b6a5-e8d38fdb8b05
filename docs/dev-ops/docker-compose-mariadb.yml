version: '3.8'

services:
  mariadb:
    image: mariadb:11.7
    container_name: mariadb-ai-agent
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: ai_agent_db
      MYSQL_USER: ai_user
      MYSQL_PASSWORD: ai_pass
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./mariadb/init:/docker-entrypoint-initdb.d
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-time-zone='+8:00'
      --innodb-buffer-pool-size=512M
      --max-connections=200
      --innodb-log-file-size=256M
      --innodb-log-buffer-size=64M
    networks:
      - ai-agent-network

volumes:
  mariadb_data:
    driver: local

networks:
  ai-agent-network:
    driver: bridge
