#!/bin/bash

# RAG服务启动脚本
# 启动MariaDB数据库服务（统一数据源）

echo "启动RAG存储服务..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 停止可能存在的容器
echo "停止现有容器..."
docker-compose -f docker-compose-mariadb.yml down

# 启动数据库服务
echo "启动MariaDB数据库服务..."
docker-compose -f docker-compose-mariadb.yml up -d

# 等待服务启动
echo "等待数据库服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
docker-compose -f docker-compose-mariadb.yml ps

# 测试MariaDB连接
echo "测试MariaDB连接..."
docker exec mariadb-ai-agent mysql -uroot -p123456 -e "SELECT 'MariaDB连接成功' as status;"

# 检查数据库和表
echo "检查数据库和表..."
docker exec mariadb-ai-agent mysql -uroot -p123456 -e "SHOW DATABASES;"
docker exec mariadb-ai-agent mysql -uroot -p123456 ai_agent_db -e "SHOW TABLES;"

echo "RAG存储服务启动完成！"
echo ""
echo "服务信息:"
echo "- MariaDB (统一数据库): localhost:3306"
echo ""
echo "配置信息:"
echo "- 数据库: ai_agent_db"
echo "- 用户名: root"
echo "- 密码: 123456"
echo ""
echo "功能说明:"
echo "- 业务数据存储: ai_client_*, ai_rag_* 表"
echo "- 向量数据存储: vector_store 表 (Spring AI自动创建)"
