# RAG存储服务使用指南

## 概述

本系统实现了基于MariaDB的RAG（检索增强生成）存储服务，统一使用MariaDB作为数据源，既支持业务数据存储，也支持向量数据存储。

## 系统架构

```
应用层 (Spring Boot)
    ↓
业务层 (RAG Storage Service)
    ↓
数据层 (MariaDB - 统一数据源)
    ├── 业务表 (ai_client_*, ai_rag_*)
    └── 向量表 (vector_store)
    ↓
Spring AI (Vector Store + Embedding)
```

## 技术栈

- **Spring AI 1.0.1**: AI应用框架
- **MariaDB 11.7+**: 统一数据库（业务数据 + 向量存储）
- **Spring Boot 3.4.3**: 应用框架
- **OpenAI Embedding**: 文本嵌入模型

## 快速开始

### 1. 环境准备

#### 启动数据库服务
```bash
cd docs/dev-ops
chmod +x start-rag-services.sh
./start-rag-services.sh
```

#### 配置环境变量
```bash
export OPENAI_API_KEY=your-openai-api-key
export OPENAI_BASE_URL=https://api.openai.com
```

### 2. 应用配置

在 `application-dev.yml` 中配置数据源：

```yaml
spring:
  datasource:
    # MariaDB 统一数据源配置
    username: root
    password: 123456
    url: *****************************************
    driver-class-name: org.mariadb.jdbc.Driver
  ai:
    openai:
      api-key: ${OPENAI_API_KEY}
      embedding:
        model: text-embedding-3-small
        dimensions: 1536
    vectorstore:
      mariadb:
        initialize-schema: true
        distance-type: COSINE
        dimensions: 1536
```

### 3. 启动应用

```bash
mvn spring-boot:run
```

## API使用指南

### 知识库管理

#### 创建知识库
```bash
curl -X POST http://localhost:8091/api/rag/knowledge-base \
  -H "Content-Type: application/json" \
  -d '{
    "ragName": "my-knowledge-base",
    "knowledgeTag": "technology"
  }'
```

#### 删除知识库
```bash
curl -X DELETE http://localhost:8091/api/rag/knowledge-base/my-knowledge-base
```

#### 获取知识库统计信息
```bash
curl http://localhost:8091/api/rag/knowledge-base/my-knowledge-base/stats
```

### 文档管理

#### 添加文档
```bash
curl -X POST http://localhost:8091/api/rag/knowledge-base/my-knowledge-base/documents \
  -H "Content-Type: application/json" \
  -d '[
    {
      "id": "doc1",
      "content": "这是一个示例文档内容",
      "metadata": {
        "category": "example",
        "author": "system"
      }
    }
  ]'
```

#### 删除文档
```bash
curl -X DELETE http://localhost:8091/api/rag/knowledge-base/my-knowledge-base/documents \
  -H "Content-Type: application/json" \
  -d '["doc1", "doc2"]'
```

#### 获取所有文档
```bash
curl http://localhost:8091/api/rag/knowledge-base/my-knowledge-base/documents
```

### 向量搜索

#### 执行搜索
```bash
curl -X POST "http://localhost:8091/api/rag/knowledge-base/my-knowledge-base/search?query=示例&topK=5&similarityThreshold=0.0"
```

#### 带过滤条件的搜索
```bash
curl -X POST "http://localhost:8091/api/rag/knowledge-base/my-knowledge-base/search?query=示例&topK=5&filterExpression=category=='example'"
```

## 测试功能

### 创建示例知识库
```bash
curl -X POST http://localhost:8091/api/rag-test/create-sample-knowledge-base?ragName=test-kb
```

### 测试搜索
```bash
curl "http://localhost:8091/api/rag-test/test-search?ragName=test-kb&query=Spring AI&topK=3"
```

### 测试RAG问答
```bash
curl -X POST "http://localhost:8091/api/rag-test/test-qa?ragName=test-kb&question=什么是Spring AI？"
```

### 测试向量存储
```bash
curl http://localhost:8091/api/rag-test/test-vector-store
```

### 批量添加文档测试
```bash
curl -X POST "http://localhost:8091/api/rag-test/test-batch-add?ragName=test-kb&documentCount=10"
```

### 清理测试数据
```bash
curl -X DELETE "http://localhost:8091/api/rag-test/cleanup?ragName=test-kb"
```

## 核心组件说明

### 1. 数据源配置 (DataSourceConfig)
- 配置MariaDB统一数据源
- 支持业务数据和向量数据存储
- 提供统一的事务管理

### 2. 向量存储配置 (VectorStoreConfig)
- 配置MariaDB向量存储
- 设置嵌入维度和距离类型
- 自动初始化向量存储表结构

### 3. RAG存储服务 (RagStorageServiceImpl)
- 知识库生命周期管理
- 文档添加、删除、搜索
- 向量相似性搜索
- 元数据过滤

### 4. 文档处理工具 (DocumentProcessor)
- 文本内容分块处理
- 文档元数据管理
- 内容清理和验证

## 数据库表结构

### MariaDB表（统一数据库：ai_agent_db）

#### 业务数据表
- `ai_client_system_prompt`: 系统提示词表
- `ai_client_model`: 模型配置表
- `ai_client_advisor`: 顾问配置表
- `ai_client_tool_mcp`: MCP工具配置表
- `ai_client`: 客户端主表
- `ai_rag_knowledge_base`: 知识库信息表
- `ai_rag_document`: 文档信息表
- `ai_rag_document_chunk`: 文档分块信息表

#### 向量数据表
- `vector_store`: 向量存储表（由Spring AI自动创建）

## 配置参数说明

### 向量存储配置
- `dimensions`: 向量维度（默认1536）
- `distance-type`: 距离计算类型（COSINE/EUCLIDEAN）
- `initialize-schema`: 是否自动初始化表结构
- `table-name`: 向量存储表名

### 嵌入模型配置
- `model`: 嵌入模型名称
- `api-key`: OpenAI API密钥
- `base-url`: API基础URL

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MariaDB服务是否启动
   - 验证连接配置是否正确
   - 确认MariaDB版本支持向量功能（11.7+）

2. **嵌入模型调用失败**
   - 检查OpenAI API密钥是否正确
   - 验证网络连接是否正常
   - 确认API配额是否充足

3. **文档搜索无结果**
   - 检查知识库是否存在
   - 验证文档是否已正确添加
   - 调整相似度阈值参数

4. **内存不足**
   - 调整JVM堆内存大小
   - 减少批量处理的文档数量
   - 优化文档分块大小

### 日志查看
```bash
# 查看应用日志
tail -f logs/application.log

# 查看MariaDB日志
docker logs mariadb-ai-agent
```

## 性能优化建议

1. **向量存储优化**
   - 合理设置向量维度
   - 使用适当的距离计算类型
   - 定期清理无用数据

2. **文档处理优化**
   - 控制文档分块大小
   - 批量处理文档
   - 异步处理大量文档

3. **搜索性能优化**
   - 使用合适的topK值
   - 添加元数据过滤条件
   - 缓存常用搜索结果

## 扩展功能

1. **支持更多文档格式**
   - PDF文档解析
   - Word文档处理
   - 网页内容抓取

2. **高级搜索功能**
   - 混合搜索（关键词+向量）
   - 多模态搜索
   - 搜索结果重排序

3. **监控和分析**
   - 搜索性能监控
   - 用户行为分析
   - 知识库质量评估
