package cn.iflytek.trigger.http.controller;

import cn.iflytek.domain.agent.model.AiRagOrder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RAG存储管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/rag")
public class RagStorageController {

    @Resource
    private IRagStorageService ragStorageService;

    /**
     * 创建知识库
     */
    @PostMapping("/knowledge-base")
    public Map<String, Object> createKnowledgeBase(@RequestBody AiRagOrder ragOrder) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("创建知识库请求: {}", ragOrder);
            
            if (ragOrder.getRagName() == null || ragOrder.getRagName().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "知识库名称不能为空");
                return result;
            }
            
            // 检查知识库是否已存在
            if (ragStorageService.knowledgeBaseExists(ragOrder.getRagName())) {
                result.put("success", false);
                result.put("message", "知识库已存在: " + ragOrder.getRagName());
                return result;
            }
            
            boolean success = ragStorageService.createKnowledgeBase(ragOrder);
            result.put("success", success);
            result.put("message", success ? "知识库创建成功" : "知识库创建失败");
            result.put("ragName", ragOrder.getRagName());
            
        } catch (Exception e) {
            log.error("创建知识库失败", e);
            result.put("success", false);
            result.put("message", "创建知识库失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 删除知识库
     */
    @DeleteMapping("/knowledge-base/{ragName}")
    public Map<String, Object> deleteKnowledgeBase(@PathVariable String ragName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("删除知识库请求: {}", ragName);
            
            if (!ragStorageService.knowledgeBaseExists(ragName)) {
                result.put("success", false);
                result.put("message", "知识库不存在: " + ragName);
                return result;
            }
            
            boolean success = ragStorageService.deleteKnowledgeBase(ragName);
            result.put("success", success);
            result.put("message", success ? "知识库删除成功" : "知识库删除失败");
            
        } catch (Exception e) {
            log.error("删除知识库失败: {}", ragName, e);
            result.put("success", false);
            result.put("message", "删除知识库失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 添加文档到知识库
     */
    @PostMapping("/knowledge-base/{ragName}/documents")
    public Map<String, Object> addDocuments(@PathVariable String ragName, 
                                           @RequestBody List<Map<String, Object>> documentData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("添加文档到知识库: {}, 文档数量: {}", ragName, documentData.size());
            
            if (!ragStorageService.knowledgeBaseExists(ragName)) {
                result.put("success", false);
                result.put("message", "知识库不存在: " + ragName);
                return result;
            }
            
            // 转换为Document对象
            List<Document> documents = documentData.stream()
                .map(data -> {
                    String id = (String) data.get("id");
                    String content = (String) data.get("content");
                    @SuppressWarnings("unchecked")
                    Map<String, Object> metadata = (Map<String, Object>) data.getOrDefault("metadata", new HashMap<>());
                    
                    return new Document(id, content, metadata);
                })
                .toList();
            
            boolean success = ragStorageService.addDocuments(ragName, documents);
            result.put("success", success);
            result.put("message", success ? "文档添加成功" : "文档添加失败");
            result.put("documentCount", documents.size());
            
        } catch (Exception e) {
            log.error("添加文档失败: 知识库={}", ragName, e);
            result.put("success", false);
            result.put("message", "添加文档失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 从知识库删除文档
     */
    @DeleteMapping("/knowledge-base/{ragName}/documents")
    public Map<String, Object> deleteDocuments(@PathVariable String ragName, 
                                              @RequestBody List<String> documentIds) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("从知识库删除文档: {}, 文档ID: {}", ragName, documentIds);
            
            boolean success = ragStorageService.deleteDocuments(ragName, documentIds);
            result.put("success", success);
            result.put("message", success ? "文档删除成功" : "文档删除失败");
            result.put("deletedCount", documentIds.size());
            
        } catch (Exception e) {
            log.error("删除文档失败: 知识库={}, 文档ID={}", ragName, documentIds, e);
            result.put("success", false);
            result.put("message", "删除文档失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 向量搜索
     */
    @PostMapping("/knowledge-base/{ragName}/search")
    public Map<String, Object> vectorSearch(@PathVariable String ragName,
                                           @RequestParam String query,
                                           @RequestParam(defaultValue = "5") int topK,
                                           @RequestParam(defaultValue = "0.0") double similarityThreshold,
                                           @RequestParam(required = false) String filterExpression) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("向量搜索: 知识库={}, 查询={}, topK={}", ragName, query, topK);
            
            List<Document> searchResults = ragStorageService.vectorSearch(
                ragName, query, topK, similarityThreshold, filterExpression);
            
            result.put("success", true);
            result.put("query", query);
            result.put("resultCount", searchResults.size());
            result.put("results", searchResults.stream()
                .map(doc -> {
                    assert doc.getText() != null;
                    return Map.of(
                        "id", doc.getId(),
                        "content", doc.getText(),
                        "metadata", doc.getMetadata()
                    );
                })
                .toList());
            
        } catch (Exception e) {
            log.error("向量搜索失败: 知识库={}, 查询={}", ragName, query, e);
            result.put("success", false);
            result.put("message", "搜索失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取知识库统计信息
     */
    @GetMapping("/knowledge-base/{ragName}/stats")
    public Map<String, Object> getKnowledgeBaseStats(@PathVariable String ragName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> stats = ragStorageService.getKnowledgeBaseStats(ragName);
            result.put("success", true);
            result.put("ragName", ragName);
            result.putAll(stats);
            
        } catch (Exception e) {
            log.error("获取知识库统计信息失败: {}", ragName, e);
            result.put("success", false);
            result.put("message", "获取统计信息失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 清空知识库
     */
    @DeleteMapping("/knowledge-base/{ragName}/clear")
    public Map<String, Object> clearKnowledgeBase(@PathVariable String ragName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("清空知识库: {}", ragName);
            
            boolean success = ragStorageService.clearKnowledgeBase(ragName);
            result.put("success", success);
            result.put("message", success ? "知识库清空成功" : "知识库清空失败");
            
        } catch (Exception e) {
            log.error("清空知识库失败: {}", ragName, e);
            result.put("success", false);
            result.put("message", "清空知识库失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取知识库中的所有文档
     */
    @GetMapping("/knowledge-base/{ragName}/documents")
    public Map<String, Object> getAllDocuments(@PathVariable String ragName) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Document> documents = ragStorageService.getAllDocuments(ragName);
            
            result.put("success", true);
            result.put("ragName", ragName);
            result.put("documentCount", documents.size());
            result.put("documents", documents.stream()
                .map(doc -> Map.of(
                    "id", doc.getId(),
                    "content", doc.getText().length() > 200 ? 
                        doc.getText().substring(0, 200) + "..." : doc.getText(),
                    "metadata", doc.getMetadata()
                ))
                .toList());
            
        } catch (Exception e) {
            log.error("获取所有文档失败: {}", ragName, e);
            result.put("success", false);
            result.put("message", "获取文档失败: " + e.getMessage());
        }
        
        return result;
    }
}
