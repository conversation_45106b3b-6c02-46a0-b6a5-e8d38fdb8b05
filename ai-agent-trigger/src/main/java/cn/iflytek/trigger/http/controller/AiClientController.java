package cn.iflytek.trigger.http.controller;

import cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * AI客户端控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ai-client")
public class AiClientController {

    @Resource
    private DefaultArmoryStrategyFactory armoryStrategyFactory;

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 初始化AI客户端
     */
    @PostMapping("/initialize")
    public Map<String, Object> initializeAiClients() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            armoryStrategyFactory.assembleAiClients();
            result.put("success", true);
            result.put("message", "AI客户端初始化成功");
            log.info("AI客户端初始化成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "AI客户端初始化失败: " + e.getMessage());
            log.error("AI客户端初始化失败", e);
        }
        
        return result;
    }

    /**
     * 重新组装指定AI客户端
     */
    @PostMapping("/reassemble/{clientId}")
    public Map<String, Object> reassembleAiClient(@PathVariable Long clientId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            armoryStrategyFactory.reassembleAiClient(clientId);
            result.put("success", true);
            result.put("message", "AI客户端重新组装成功");
            log.info("AI客户端重新组装成功: {}", clientId);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "AI客户端重新组装失败: " + e.getMessage());
            log.error("AI客户端重新组装失败: {}", clientId, e);
        }
        
        return result;
    }

    /**
     * 测试AI客户端对话
     */
    @PostMapping("/chat/{clientId}")
    public Map<String, Object> chat(@PathVariable Long clientId, @RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String beanName = "ChatClient_" + clientId;
            ChatClient chatClient = applicationContext.getBean(beanName, ChatClient.class);
            
            String userMessage = request.get("message");
            if (userMessage == null || userMessage.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "消息内容不能为空");
                return result;
            }
            
            String response = chatClient.prompt()
                .user(userMessage)
                .call()
                .content();
            
            result.put("success", true);
            result.put("response", response);
            log.info("AI客户端对话成功: clientId={}, message={}", clientId, userMessage);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "对话失败: " + e.getMessage());
            log.error("AI客户端对话失败: clientId={}", clientId, e);
        }
        
        return result;
    }

    /**
     * 获取已注册的AI客户端列表
     */
    @GetMapping("/list")
    public Map<String, Object> listAiClients() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String[] beanNames = applicationContext.getBeanNamesForType(ChatClient.class);
            result.put("success", true);
            result.put("clients", beanNames);
            result.put("count", beanNames.length);
            log.info("获取AI客户端列表成功，数量: {}", beanNames.length);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取AI客户端列表失败: " + e.getMessage());
            log.error("获取AI客户端列表失败", e);
        }
        
        return result;
    }
}
