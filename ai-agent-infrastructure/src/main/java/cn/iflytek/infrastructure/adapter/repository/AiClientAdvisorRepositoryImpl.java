package cn.iflytek.infrastructure.adapter.repository;

import cn.iflytek.domain.agent.adapter.repository.AiClientAdvisorRepository;
import cn.iflytek.domain.agent.model.AiClientAdvisor;
import cn.iflytek.infrastructure.dao.AiClientAdvisorMapper;
import cn.iflytek.infrastructure.dao.po.AiClientAdvisorPO;
import org.springframework.stereotype.Repository;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI客户端顾问配置仓储实现
 */
@Repository
public class AiClientAdvisorRepositoryImpl implements AiClientAdvisorRepository {

    @Resource
    private AiClientAdvisorMapper advisorMapper;

    @Override
    public Long save(AiClientAdvisor advisor) {
        AiClientAdvisorPO advisorPO = convertToAiClientAdvisorPO(advisor);
        advisorMapper.insert(advisorPO);
        return advisorPO.getId();
    }

    @Override
    public AiClientAdvisor findById(Long id) {
        AiClientAdvisorPO advisorPO = advisorMapper.selectById(id);
        if (advisorPO == null) {
            return null;
        }

        return convertToAiClientAdvisor(advisorPO);
    }

    @Override
    public AiClientAdvisor findByAdvisorName(String advisorName) {
        AiClientAdvisorPO advisorPO = advisorMapper.selectByAdvisorName(advisorName);
        if (advisorPO == null) {
            return null;
        }

        return convertToAiClientAdvisor(advisorPO);
    }

    @Override
    public List<AiClientAdvisor> findByAdvisorType(String advisorType) {
        List<AiClientAdvisorPO> advisorPOList = advisorMapper.selectByAdvisorType(advisorType);
        return advisorPOList.stream()
                .map(this::convertToAiClientAdvisor)
                .collect(Collectors.toList());
    }

    @Override
    public List<AiClientAdvisor> findAll() {
        List<AiClientAdvisorPO> advisorPOList = advisorMapper.selectAll();
        return advisorPOList.stream()
                .map(this::convertToAiClientAdvisor)
                .collect(Collectors.toList());
    }

    @Override
    public boolean update(AiClientAdvisor advisor) {
        AiClientAdvisorPO advisorPO = convertToAiClientAdvisorPO(advisor);
        int result = advisorMapper.updateById(advisorPO);
        return result > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        int result = advisorMapper.deleteById(id);
        return result > 0;
    }

    private AiClientAdvisorPO convertToAiClientAdvisorPO(AiClientAdvisor advisor) {
        AiClientAdvisorPO.AiClientAdvisorPOBuilder builder = AiClientAdvisorPO.builder()
                .id(advisor.getId())
                .advisorName(advisor.getAdvisorName())
                .advisorType(advisor.getAdvisorType())
                .orderNum(advisor.getOrderNum());

        // 处理ChatMemory配置
        if (advisor.getChatMemory() != null) {
            builder.chatMemoryMaxMessages(advisor.getChatMemory().getMaxMessages());
        }

        // 处理RagAnswer配置
        if (advisor.getRagAnswer() != null) {
            builder.ragAnswerTopK(advisor.getRagAnswer().getTopK())
                    .ragAnswerFilterExpression(advisor.getRagAnswer().getFilterExpression());
        }

        return builder.build();
    }

    private AiClientAdvisor convertToAiClientAdvisor(AiClientAdvisorPO advisorPO) {
        AiClientAdvisor.AiClientAdvisorBuilder builder = AiClientAdvisor.builder()
                .id(advisorPO.getId())
                .advisorName(advisorPO.getAdvisorName())
                .advisorType(advisorPO.getAdvisorType())
                .orderNum(advisorPO.getOrderNum());

        // 构建ChatMemory
        if (advisorPO.getChatMemoryMaxMessages() != null) {
            AiClientAdvisor.ChatMemory chatMemory = AiClientAdvisor.ChatMemory.builder()
                    .maxMessages(advisorPO.getChatMemoryMaxMessages())
                    .build();
            builder.chatMemory(chatMemory);
        }

        // 构建RagAnswer
        if (advisorPO.getRagAnswerTopK() != null || advisorPO.getRagAnswerFilterExpression() != null) {
            AiClientAdvisor.RagAnswer ragAnswer = AiClientAdvisor.RagAnswer.builder()
                    .topK(advisorPO.getRagAnswerTopK() != null ? advisorPO.getRagAnswerTopK() : 4)
                    .filterExpression(advisorPO.getRagAnswerFilterExpression())
                    .build();
            builder.ragAnswer(ragAnswer);
        }

        return builder.build();
    }
}
