package cn.iflytek.domain.agent.service.rag.service;

import cn.iflytek.domain.agent.model.AiRagOrder;
import org.springframework.ai.document.Document;

import java.util.List;
import java.util.Map;

/**
 * RAG存储服务接口
 * 提供知识库管理、文档存储和向量搜索功能
 */
public interface IRagStorageService {

    /**
     * 创建知识库
     *
     * @param ragOrder 知识库创建请求对象，包含知识库名称和标签信息
     * @return 创建成功返回true，失败返回false
     */
    boolean createKnowledgeBase(AiRagOrder ragOrder);

    /**
     * 删除知识库
     * 删除指定知识库及其包含的所有文档
     *
     * @param ragName 知识库名称
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteKnowledgeBase(String ragName);

    /**
     * 向知识库添加文档
     *
     * @param ragName 知识库名称
     * @param documents 要添加的文档列表
     * @return 添加成功返回true，失败返回false
     */
    boolean addDocuments(String ragName, List<Document> documents);

    /**
     * 从知识库删除指定文档
     *
     * @param ragName 知识库名称
     * @param documentIds 要删除的文档ID列表
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteDocuments(String ragName, List<String> documentIds);

    /**
     * 向量相似性搜索
     * 在指定知识库中进行向量搜索，返回最相似的文档
     *
     * @param ragName 知识库名称
     * @param query 搜索查询文本
     * @param topK 返回结果数量
     * @param similarityThreshold 相似度阈值
     * @param filterExpression 过滤表达式
     * @return 搜索结果文档列表
     */
    List<Document> vectorSearch(String ragName, String query, int topK,
                               double similarityThreshold, String filterExpression);

    /**
     * 获取知识库中的所有文档
     *
     * @param ragName 知识库名称
     * @return 文档列表
     */
    List<Document> getAllDocuments(String ragName);

    /**
     * 检查知识库是否存在
     *
     * @param ragName 知识库名称
     * @return 存在返回true，不存在返回false
     */
    boolean knowledgeBaseExists(String ragName);

    /**
     * 获取知识库统计信息
     * 包括文档数量、创建时间、标签等信息
     *
     * @param ragName 知识库名称
     * @return 包含统计信息的Map对象
     */
    Map<String, Object> getKnowledgeBaseStats(String ragName);

    /**
     * 清空知识库
     * 删除知识库中的所有文档，但保留知识库本身
     *
     * @param ragName 知识库名称
     * @return 清空成功返回true，失败返回false
     */
    boolean clearKnowledgeBase(String ragName);
}
