package cn.iflytek.domain.agent.service.rag.service.impl;

import cn.iflytek.domain.agent.model.AiRagOrder;
import cn.iflytek.domain.agent.service.rag.service.IRagStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * RAG存储服务实现
 */
@Slf4j
@Service
public class RagStorageServiceImpl implements IRagStorageService {

    @Resource
    private VectorStore vectorStore;

    @Override
    public boolean createKnowledgeBase(AiRagOrder ragOrder) {
        try {
            log.info("创建知识库: {}, 标签: {}", ragOrder.getRagName(), ragOrder.getKnowledgeTag());

            // 创建一个标识文档来表示知识库的存在
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("type", "knowledge_base");
            metadata.put("rag_name", ragOrder.getRagName());
            metadata.put("knowledge_tag", ragOrder.getKnowledgeTag() != null ? ragOrder.getKnowledgeTag() : "");
            metadata.put("created_at", System.currentTimeMillis());

            Document knowledgeBaseDoc = new Document(
                "knowledge_base_" + ragOrder.getRagName(),
                "Knowledge Base: " + ragOrder.getRagName(),
                metadata
            );

            vectorStore.add(List.of(knowledgeBaseDoc));
            log.info("知识库创建成功: {}", ragOrder.getRagName());
            return true;
        } catch (Exception e) {
            log.error("创建知识库失败: {}", ragOrder.getRagName(), e);
            return false;
        }
    }

    @Override
    public boolean deleteKnowledgeBase(String ragName) {
        try {
            log.info("删除知识库: {}", ragName);

            // 简化实现：通过搜索获取所有相关文档并删除
            List<Document> documents = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("*")
                    .topK(10000)
                    .build()
            );

            // 过滤出属于指定知识库的文档
            List<String> documentIds = documents.stream()
                .filter(doc -> ragName.equals(doc.getMetadata().get("rag_name")))
                .map(Document::getId)
                .collect(Collectors.toList());

            if (!documentIds.isEmpty()) {
                vectorStore.delete(documentIds);
            }

            log.info("知识库删除成功: {}, 删除文档数量: {}", ragName, documentIds.size());
            return true;
        } catch (Exception e) {
            log.error("删除知识库失败: {}", ragName, e);
            return false;
        }
    }

    @Override
    public boolean addDocuments(String ragName, List<Document> documents) {
        try {
            log.info("向知识库 {} 添加 {} 个文档", ragName, documents.size());

            // 为每个文档添加知识库标识
            List<Document> enrichedDocuments = documents.stream()
                .map(doc -> {
                    Map<String, Object> metadata = new HashMap<>(doc.getMetadata());
                    metadata.put("rag_name", ragName);
                    metadata.put("type", "document");
                    metadata.put("added_at", System.currentTimeMillis());

                    return new Document(doc.getId(), doc.getText(), metadata);
                })
                .collect(Collectors.toList());

            vectorStore.add(enrichedDocuments);
            log.info("文档添加成功: 知识库={}, 文档数量={}", ragName, documents.size());
            return true;
        } catch (Exception e) {
            log.error("添加文档失败: 知识库={}, 文档数量={}", ragName, documents.size(), e);
            return false;
        }
    }

    @Override
    public List<Document> vectorSearch(String ragName, String query, int topK) {
        try {
            log.debug("向量搜索: 知识库={}, 查询={}, topK={}", ragName, query, topK);

            // 简化搜索实现
            List<Document> allResults = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query(query)
                    .topK(topK * 2) // 获取更多结果用于过滤
                    .build()
            );

            // 过滤出属于指定知识库的文档
            List<Document> filteredResults = allResults.stream()
                .filter(doc -> ragName.equals(doc.getMetadata().get("rag_name")))
                .filter(doc -> "document".equals(doc.getMetadata().get("type")))
                .limit(topK)
                .collect(Collectors.toList());

            log.debug("搜索完成: 知识库={}, 结果数量={}", ragName, filteredResults.size());
            return filteredResults;
        } catch (Exception e) {
            log.error("向量搜索失败: 知识库={}, 查询={}", ragName, query, e);
            return List.of();
        }
    }

    @Override
    public boolean knowledgeBaseExists(String ragName) {
        try {
            // 简化实现：搜索所有文档并过滤
            List<Document> allDocs = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("*")
                    .topK(1000)
                    .build()
            );

            // 检查是否存在知识库标识文档
            boolean exists = allDocs.stream()
                .anyMatch(doc -> ragName.equals(doc.getMetadata().get("rag_name"))
                    && "knowledge_base".equals(doc.getMetadata().get("type")));

            return exists;
        } catch (Exception e) {
            log.error("检查知识库存在性失败: {}", ragName, e);
            return false;
        }
    }
}
